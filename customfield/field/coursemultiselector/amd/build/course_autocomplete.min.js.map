{"version": 3, "file": "course_autocomplete.min.js", "sources": ["../src/course_autocomplete.js"], "sourcesContent": ["/**\n * Course autocomplete with status-based styling\n *\n * @module     customfield_coursemultiselector/course_autocomplete\n * @copyright  2025 REVVO <www.revvo.com.br>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\ndefine(['jquery'], function($) {\n    'use strict';\n\n    /**\n     * Initialize course autocomplete with status styling\n     *\n     * @param {string} elementId The ID of the autocomplete element\n     * @param {object} courseData Course data with status information\n     */\n    function init(elementId, courseData) {\n        \n        /**\n         * Style tags based on course status\n         */\n        function styleCourseTags() {\n            var container = $('#' + elementId + '_container');\n            if (!container.length) {\n                container = $('#' + elementId).closest('.form-autocomplete-container');\n            }\n            \n            container.find('.form-autocomplete-selection [data-value]').each(function() {\n                var courseId = $(this).data('value');\n                var tag = $(this);\n                \n                if (courseData[courseId]) {\n                    var status = courseData[courseId].status;\n                    \n                    // Remove existing status classes\n                    tag.removeClass('coursemultiselector-hidden coursemultiselector-future coursemultiselector-expired coursemultiselector-active');\n                    \n                    // Add new status class\n                    tag.addClass('coursemultiselector-tag coursemultiselector-' + status);\n                    \n                    // Add title with status information\n                    var statusText = getStatusText(status);\n                    var originalTitle = tag.attr('title') || '';\n                    if (originalTitle.indexOf(statusText) === -1) {\n                        tag.attr('title', originalTitle + ' (' + statusText + ')');\n                    }\n                }\n            });\n        }\n\n        /**\n         * Get status text for display\n         *\n         * @param {string} status Course status\n         * @return {string} Status text\n         */\n        function getStatusText(status) {\n            switch (status) {\n                case 'hidden':\n                    return M.util.get_string('status_hidden', 'customfield_coursemultiselector');\n                case 'future':\n                    return M.util.get_string('status_future', 'customfield_coursemultiselector');\n                case 'expired':\n                    return M.util.get_string('status_expired', 'customfield_coursemultiselector');\n                case 'active':\n                    return M.util.get_string('status_active', 'customfield_coursemultiselector');\n                default:\n                    return '';\n            }\n        }\n\n        /**\n         * Setup event listeners and observers\n         */\n        function setupEventListeners() {\n            var element = $('#' + elementId);\n            \n            // Style tags on selection change\n            element.on('change', function() {\n                setTimeout(styleCourseTags, 100);\n            });\n            \n            // Use MutationObserver to catch dynamic changes\n            var observer = new MutationObserver(function(mutations) {\n                var shouldUpdate = false;\n                mutations.forEach(function(mutation) {\n                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {\n                        shouldUpdate = true;\n                    }\n                });\n                \n                if (shouldUpdate) {\n                    setTimeout(styleCourseTags, 50);\n                }\n            });\n            \n            var container = document.getElementById(elementId + '_container');\n            if (!container) {\n                container = element.closest('.form-autocomplete-container')[0];\n            }\n            \n            if (container) {\n                observer.observe(container, { \n                    childList: true, \n                    subtree: true,\n                    attributes: false,\n                    characterData: false\n                });\n            }\n        }\n\n        // Initialize when DOM is ready\n        $(document).ready(function() {\n            // Initial styling\n            setTimeout(styleCourseTags, 200);\n            \n            // Setup event listeners\n            setupEventListeners();\n            \n            // Periodic check for late-loading elements\n            var checkCount = 0;\n            var checkInterval = setInterval(function() {\n                styleCourseTags();\n                checkCount++;\n                \n                if (checkCount >= 10) { // Stop after 10 attempts (5 seconds)\n                    clearInterval(checkInterval);\n                }\n            }, 500);\n        });\n    }\n\n    return {\n        init: init\n    };\n});\n"], "names": ["define", "$", "init", "elementId", "courseData", "styleCourseTags", "container", "length", "closest", "find", "each", "courseId", "this", "data", "tag", "status", "removeClass", "addClass", "statusText", "M", "util", "get_string", "getStatusText", "originalTitle", "attr", "indexOf", "document", "ready", "setTimeout", "element", "on", "observer", "MutationObserver", "mutations", "shouldUpdate", "for<PERSON>ach", "mutation", "type", "addedNodes", "getElementById", "observe", "childList", "subtree", "attributes", "characterData", "setupEventListeners", "checkCount", "checkInterval", "setInterval", "clearInterval"], "mappings": ";;;;;;;AAQAA,6DAAO,CAAC,WAAW,SAASC,SA6HjB,CACHC,cArHUC,UAAWC,qBAKZC,sBACDC,UAAYL,EAAE,IAAME,UAAY,cAC/BG,UAAUC,SACXD,UAAYL,EAAE,IAAME,WAAWK,QAAQ,iCAG3CF,UAAUG,KAAK,6CAA6CC,MAAK,eACzDC,SAAWV,EAAEW,MAAMC,KAAK,SACxBC,IAAMb,EAAEW,SAERR,WAAWO,UAAW,KAClBI,OAASX,WAAWO,UAAUI,OAGlCD,IAAIE,YAAY,gHAGhBF,IAAIG,SAAS,+CAAiDF,YAG1DG,oBAeOH,eACXA,YACC,gBACMI,EAAEC,KAAKC,WAAW,gBAAiB,uCACzC,gBACMF,EAAEC,KAAKC,WAAW,gBAAiB,uCACzC,iBACMF,EAAEC,KAAKC,WAAW,iBAAkB,uCAC1C,gBACMF,EAAEC,KAAKC,WAAW,gBAAiB,iDAEnC,IA1BUC,CAAcP,QAC3BQ,cAAgBT,IAAIU,KAAK,UAAY,IACE,IAAvCD,cAAcE,QAAQP,aACtBJ,IAAIU,KAAK,QAASD,cAAgB,KAAOL,WAAa,SAoEtEjB,EAAEyB,UAAUC,OAAM,WAEdC,WAAWvB,gBAAiB,oBAvCxBwB,QAAU5B,EAAE,IAAME,WAGtB0B,QAAQC,GAAG,UAAU,WACjBF,WAAWvB,gBAAiB,YAI5B0B,SAAW,IAAIC,kBAAiB,SAASC,eACrCC,cAAe,EACnBD,UAAUE,SAAQ,SAASC,UACD,cAAlBA,SAASC,MAAwBD,SAASE,WAAW/B,OAAS,IAC9D2B,cAAe,MAInBA,cACAN,WAAWvB,gBAAiB,OAIhCC,UAAYoB,SAASa,eAAepC,UAAY,cAC/CG,YACDA,UAAYuB,QAAQrB,QAAQ,gCAAgC,IAG5DF,WACAyB,SAASS,QAAQlC,UAAW,CACxBmC,WAAW,EACXC,SAAS,EACTC,YAAY,EACZC,eAAe,IAWvBC,OAGIC,WAAa,EACbC,cAAgBC,aAAY,WAC5B3C,oBACAyC,YAEkB,IACdG,cAAcF,iBAEnB,SAOd"}