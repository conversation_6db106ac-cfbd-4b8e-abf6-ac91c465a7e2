/**
 * Course autocomplete with status-based styling
 *
 * @module     customfield_coursemultiselector/course_autocomplete
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
define("customfield_coursemultiselector/course_autocomplete",["jquery"],(function($){return{init:function(elementId,courseData){function styleCourseTags(){var container=$("#"+elementId+"_container");container.length||(container=$("#"+elementId).closest(".form-autocomplete-container")),container.find(".form-autocomplete-selection [data-value]").each((function(){var courseId=$(this).data("value"),tag=$(this);if(courseData[courseId]){var status=courseData[courseId].status;tag.removeClass("coursemultiselector-hidden coursemultiselector-future coursemultiselector-expired coursemultiselector-active"),tag.addClass("coursemultiselector-tag coursemultiselector-"+status);var statusText=function(status){switch(status){case"hidden":return M.util.get_string("status_hidden","customfield_coursemultiselector");case"future":return M.util.get_string("status_future","customfield_coursemultiselector");case"expired":return M.util.get_string("status_expired","customfield_coursemultiselector");case"active":return M.util.get_string("status_active","customfield_coursemultiselector");default:return""}}(status),originalTitle=tag.attr("title")||"";-1===originalTitle.indexOf(statusText)&&tag.attr("title",originalTitle+" ("+statusText+")")}}))}$(document).ready((function(){setTimeout(styleCourseTags,200),function(){var element=$("#"+elementId);element.on("change",(function(){setTimeout(styleCourseTags,100)}));var observer=new MutationObserver((function(mutations){var shouldUpdate=!1;mutations.forEach((function(mutation){"childList"===mutation.type&&mutation.addedNodes.length>0&&(shouldUpdate=!0)})),shouldUpdate&&setTimeout(styleCourseTags,50)})),container=document.getElementById(elementId+"_container");container||(container=element.closest(".form-autocomplete-container")[0]),container&&observer.observe(container,{childList:!0,subtree:!0,attributes:!1,characterData:!1})}();var checkCount=0,checkInterval=setInterval((function(){styleCourseTags(),++checkCount>=10&&clearInterval(checkInterval)}),500)}))}}}));

//# sourceMappingURL=course_autocomplete.min.js.map