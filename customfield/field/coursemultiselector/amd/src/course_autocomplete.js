/**
 * Course autocomplete with status-based styling
 *
 * @module     customfield_coursemultiselector/course_autocomplete
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

define(['jquery'], function($) {
    'use strict';

    /**
     * Initialize course autocomplete with status styling
     *
     * @param {string} elementId The ID of the autocomplete element
     * @param {object} courseData Course data with status information
     */
    function init(elementId, courseData) {
        
        /**
         * Style tags based on course status
         */
        function styleCourseTags() {
            var container = $('#' + elementId + '_container');
            if (!container.length) {
                container = $('#' + elementId).closest('.form-autocomplete-container');
            }
            
            container.find('.form-autocomplete-selection [data-value]').each(function() {
                var courseId = $(this).data('value');
                var tag = $(this);
                
                if (courseData[courseId]) {
                    var status = courseData[courseId].status;
                    
                    // Remove existing status classes
                    tag.removeClass('coursemultiselector-hidden coursemultiselector-future coursemultiselector-expired coursemultiselector-active');
                    
                    // Add new status class
                    tag.addClass('coursemultiselector-tag coursemultiselector-' + status);
                    
                    // Add title with status information
                    var statusText = getStatusText(status);
                    var originalTitle = tag.attr('title') || '';
                    if (originalTitle.indexOf(statusText) === -1) {
                        tag.attr('title', originalTitle + ' (' + statusText + ')');
                    }
                }
            });
        }

        /**
         * Get status text for display
         *
         * @param {string} status Course status
         * @return {string} Status text
         */
        function getStatusText(status) {
            switch (status) {
                case 'hidden':
                    return M.util.get_string('status_hidden', 'customfield_coursemultiselector');
                case 'future':
                    return M.util.get_string('status_future', 'customfield_coursemultiselector');
                case 'expired':
                    return M.util.get_string('status_expired', 'customfield_coursemultiselector');
                case 'active':
                    return M.util.get_string('status_active', 'customfield_coursemultiselector');
                default:
                    return '';
            }
        }

        /**
         * Setup event listeners and observers
         */
        function setupEventListeners() {
            var element = $('#' + elementId);
            
            // Style tags on selection change
            element.on('change', function() {
                setTimeout(styleCourseTags, 100);
            });
            
            // Use MutationObserver to catch dynamic changes
            var observer = new MutationObserver(function(mutations) {
                var shouldUpdate = false;
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        shouldUpdate = true;
                    }
                });
                
                if (shouldUpdate) {
                    setTimeout(styleCourseTags, 50);
                }
            });
            
            var container = document.getElementById(elementId + '_container');
            if (!container) {
                container = element.closest('.form-autocomplete-container')[0];
            }
            
            if (container) {
                observer.observe(container, { 
                    childList: true, 
                    subtree: true,
                    attributes: false,
                    characterData: false
                });
            }
        }

        // Initialize when DOM is ready
        $(document).ready(function() {
            // Initial styling
            setTimeout(styleCourseTags, 200);
            
            // Setup event listeners
            setupEventListeners();
            
            // Periodic check for late-loading elements
            var checkCount = 0;
            var checkInterval = setInterval(function() {
                styleCourseTags();
                checkCount++;
                
                if (checkCount >= 10) { // Stop after 10 attempts (5 seconds)
                    clearInterval(checkInterval);
                }
            }, 500);
        });
    }

    return {
        init: init
    };
});
